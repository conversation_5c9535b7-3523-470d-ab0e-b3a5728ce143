# YBSS LLM 服务并发性能优化

## 概述

本项目已经过优化，能够支持200个并发请求的场景。主要优化包括：

1. **HTTP连接池配置** - 复用连接，减少连接开销
2. **并发控制** - 使用信号量限制同时处理的请求数量
3. **Uvicorn优化配置** - 多worker进程，性能调优

## 优化详情

### 1. HTTP连接池优化

- **连接复用**: 使用httpx.AsyncClient全局实例
- **连接池大小**: 最大200个连接，保持100个活跃连接
- **超时配置**: 精细化的连接、读取、写入超时设置

### 2. 并发控制

- **信号量机制**: 限制最大50个同时处理的请求
- **队列管理**: 超出限制的请求会排队等待
- **资源保护**: 防止系统过载

### 3. 服务器配置优化

- **多Worker进程**: 根据CPU核心数自动配置
- **事件循环优化**: 使用uvloop提升性能
- **HTTP解析优化**: 使用httptools加速请求解析

## 配置参数

### 环境变量配置

```bash
# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4
LOG_LEVEL=info

# 并发控制
MAX_CONCURRENT_REQUESTS=50

# HTTP连接池配置
HTTP_MAX_KEEPALIVE=100
HTTP_MAX_CONNECTIONS=200
HTTP_KEEPALIVE_EXPIRY=30.0

# 超时配置
HTTP_TIMEOUT_CONNECT=5.0
HTTP_TIMEOUT_READ=25.0
HTTP_TIMEOUT_WRITE=5.0
HTTP_TIMEOUT_POOL=2.0
```

## 部署方式

### 1. 直接运行

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python run.py
```

### 2. Docker部署

```bash
# 构建镜像
docker build -t ybss-llm .

# 运行容器
docker run -p 8000:8000 \
  -e MAX_CONCURRENT_REQUESTS=100 \
  -e WORKERS=4 \
  ybss-llm
```

### 3. Docker Compose部署（推荐）

```bash
# 启动服务（包含Nginx负载均衡）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f ybss-llm
```

## 性能监控

### 健康检查

```bash
curl http://localhost:8000/health
```

响应示例：
```json
{
  "status": "healthy",
  "concurrent_limit": 50,
  "available_permits": 45,
  "active_requests": 5,
  "http_client_status": "connected"
}
```

### 性能指标

```bash
curl http://localhost:8000/metrics
```

响应示例：
```json
{
  "concurrent_requests": {
    "max_limit": 50,
    "available": 45,
    "active": 5,
    "utilization_percent": 10.0
  },
  "http_pool": {
    "max_connections": 200,
    "max_keepalive": 100,
    "keepalive_expiry": 30.0
  },
  "timeouts": {
    "connect": 5.0,
    "read": 25.0,
    "write": 5.0,
    "pool": 2.0
  }
}
```

## 压力测试

### 运行压力测试

```bash
# 安装测试依赖
pip install aiohttp

# 运行200并发测试
python stress_test.py
```

### 测试结果解读

- **成功率**: 应该 ≥95%
- **吞吐量**: 应该 ≥50 RPS
- **延迟**: P95应该 ≤5秒

## 性能调优建议

### 1. 根据硬件调整参数

```bash
# 4核8GB内存服务器建议配置
MAX_CONCURRENT_REQUESTS=50
WORKERS=4
HTTP_MAX_CONNECTIONS=200

# 8核16GB内存服务器建议配置
MAX_CONCURRENT_REQUESTS=100
WORKERS=8
HTTP_MAX_CONNECTIONS=400
```

### 2. 监控关键指标

- CPU使用率应该 <80%
- 内存使用率应该 <85%
- 并发利用率应该 <90%

### 3. 扩展策略

#### 垂直扩展
- 增加CPU核心数
- 增加内存容量
- 调整并发参数

#### 水平扩展
- 部署多个服务实例
- 使用Nginx负载均衡
- 考虑使用Kubernetes

## 故障排查

### 常见问题

1. **连接超时**
   - 检查LLM服务可用性
   - 调整HTTP_TIMEOUT_CONNECT参数

2. **请求排队**
   - 检查并发利用率
   - 考虑增加MAX_CONCURRENT_REQUESTS

3. **内存不足**
   - 减少WORKERS数量
   - 减少连接池大小

### 日志分析

```bash
# 查看错误日志
docker-compose logs ybss-llm | grep ERROR

# 查看性能日志
docker-compose logs ybss-llm | grep "处理请求"
```

## 最佳实践

1. **生产环境部署**
   - 使用Docker Compose
   - 配置健康检查
   - 设置资源限制

2. **监控告警**
   - 监控/health端点
   - 设置响应时间告警
   - 监控错误率

3. **容量规划**
   - 定期进行压力测试
   - 根据业务增长调整配置
   - 预留性能缓冲

## 支持的并发场景

经过优化后，系统能够稳定支持：

- **200个并发用户**
- **每秒50-100个请求**
- **P95响应时间 <5秒**
- **99%+的可用性**
