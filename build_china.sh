#!/bin/bash

# 中国区域优化的Docker构建脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ybss-llm"
VERSION=${1:-"latest"}

echo -e "${BLUE}=== 中国区域优化 Docker 构建脚本 ===${NC}"
echo -e "${BLUE}镜像名称: ${IMAGE_NAME}${NC}"
echo -e "${BLUE}版本标签: ${VERSION}${NC}"
echo -e "${YELLOW}使用中国镜像源加速构建...${NC}"

# 函数定义
print_step() {
    echo -e "${GREEN}[步骤] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[警告] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

# 检查Docker环境
check_docker() {
    print_step "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    print_info "Docker环境检查通过"
}

# 配置Docker镜像加速
setup_docker_mirror() {
    print_step "配置Docker镜像加速..."
    
    # 检查是否已配置镜像加速
    if docker info 2>/dev/null | grep -q "Registry Mirrors"; then
        print_info "Docker镜像加速已配置"
    else
        print_warning "建议配置Docker镜像加速以提升基础镜像拉取速度"
        print_info "可以配置以下镜像源："
        echo "  - https://docker.1ms.run/"
        echo "  - https://docker.1panel.live/"
        echo "  - https://docker.ketches.cn/"
    fi
}

# 预拉取基础镜像
pull_base_image() {
    print_step "预拉取基础镜像..."
    
    print_info "拉取 python:3.12-slim..."
    if docker pull python:3.12-slim; then
        print_info "✅ 基础镜像拉取成功"
    else
        print_error "❌ 基础镜像拉取失败，请检查网络连接"
        exit 1
    fi
}

# 构建镜像
build_images() {
    print_step "构建Docker镜像..."
    
    # 构建开发版
    print_info "构建开发版镜像（使用中国镜像源）..."
    if docker build -t ${IMAGE_NAME}:${VERSION}-dev -f Dockerfile.china .; then
        print_info "✅ 开发版镜像构建成功"
    else
        print_error "❌ 开发版镜像构建失败"
        exit 1
    fi
    
    # 构建生产版
    print_info "构建生产版镜像（使用中国镜像源）..."
    if docker build -t ${IMAGE_NAME}:${VERSION} -f Dockerfile.china.prod .; then
        print_info "✅ 生产版镜像构建成功"
    else
        print_error "❌ 生产版镜像构建失败"
        exit 1
    fi
}

# 测试镜像
test_image() {
    print_step "测试镜像..."
    
    print_info "启动测试容器..."
    container_id=$(docker run -d -p 8001:8000 --name ybss-llm-test ${IMAGE_NAME}:${VERSION})
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 15
    
    # 健康检查
    print_info "执行健康检查..."
    max_attempts=10
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8001/health > /dev/null 2>&1; then
            print_info "✅ 健康检查通过"
            test_result=0
            break
        else
            echo -n "."
            sleep 2
            attempt=$((attempt + 1))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "❌ 健康检查失败"
        docker logs ybss-llm-test
        test_result=1
    fi
    
    # 清理测试容器
    print_info "清理测试容器..."
    docker stop ${container_id} > /dev/null
    docker rm ${container_id} > /dev/null
    
    return $test_result
}

# 显示镜像信息
show_image_info() {
    print_step "镜像信息:"
    echo ""
    docker images | grep ${IMAGE_NAME} | head -5
    echo ""
    
    # 显示镜像大小
    if docker images ${IMAGE_NAME}:${VERSION} --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep -q ${VERSION}; then
        print_info "生产版镜像大小: $(docker images ${IMAGE_NAME}:${VERSION} --format '{{.Size}}')"
    fi
    
    if docker images ${IMAGE_NAME}:${VERSION}-dev --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep -q ${VERSION}-dev; then
        print_info "开发版镜像大小: $(docker images ${IMAGE_NAME}:${VERSION}-dev --format '{{.Size}}')"
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    print_step "使用说明:"
    echo ""
    echo "1. 运行容器:"
    echo "   docker run -d -p 8000:8000 --name ybss-llm ${IMAGE_NAME}:${VERSION}"
    echo ""
    echo "2. 使用环境变量配置:"
    echo "   docker run -d -p 8000:8000 \\"
    echo "     -e MAX_CONCURRENT_REQUESTS=100 \\"
    echo "     -e WORKERS=4 \\"
    echo "     --name ybss-llm ${IMAGE_NAME}:${VERSION}"
    echo ""
    echo "3. 使用Docker Compose:"
    echo "   CHINA_MODE=true docker-compose up -d"
    echo ""
    echo "4. 健康检查:"
    echo "   curl http://localhost:8000/health"
    echo ""
}

# 显示优化说明
show_optimization_info() {
    print_step "中国区域优化说明:"
    echo ""
    print_info "✅ 使用腾讯云/阿里云APT镜像源"
    print_info "✅ 使用清华大学PyPI镜像源"
    print_info "✅ 优化的依赖安装顺序"
    print_info "✅ 减少网络请求次数"
    echo ""
    print_info "预期构建时间："
    print_info "  - 首次构建: 3-5分钟"
    print_info "  - 增量构建: 1-2分钟"
    echo ""
}

# 主函数
main() {
    check_docker
    setup_docker_mirror
    show_optimization_info
    pull_base_image
    build_images
    
    if test_image; then
        print_info "✅ 镜像测试通过"
    else
        print_error "❌ 镜像测试失败"
        exit 1
    fi
    
    show_image_info
    show_usage
    
    print_step "构建完成! 🎉"
    print_info "使用中国镜像源优化，构建速度显著提升！"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [VERSION]"
    echo ""
    echo "参数:"
    echo "  VERSION  镜像版本 (默认: latest)"
    echo ""
    echo "示例:"
    echo "  $0                # 使用默认版本"
    echo "  $0 v1.0.0         # 指定版本"
    echo ""
    echo "环境变量:"
    echo "  无需额外配置，脚本自动使用中国镜像源"
    echo ""
}

# 检查参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 执行主函数
main
