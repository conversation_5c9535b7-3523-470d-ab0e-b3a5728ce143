"""
API测试脚本
"""
import requests
import json


def test_extract_api():
    """测试提取接口"""
    url = "http://localhost:8000/extract"
    
    # 测试用例1: 不开启深度思考
    test_data_1 = {
        "query": "小明和小红因为借款问题发生纠纷",
        "thinking": False
    }
    
    print("测试用例1: 不开启深度思考")
    print(f"请求数据: {json.dumps(test_data_1, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_1)
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试用例2: 开启深度思考
    test_data_2 = {
        "query": "张三和李四因为房屋租赁合同发生争议，张三要求李四支付拖欠的房租5000元",
        "thinking": True
    }
    
    print("测试用例2: 开启深度思考")
    print(f"请求数据: {json.dumps(test_data_2, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_2)
        print(f"响应状态码: {response.status_code}")
        print(f"响应数据: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"请求失败: {str(e)}")


def test_health_check():
    """测试健康检查接口"""
    url = "http://localhost:8000/health"
    
    try:
        response = requests.get(url)
        print(f"健康检查响应: {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {str(e)}")


if __name__ == "__main__":
    print("开始测试API...")
    
    # 先测试健康检查
    test_health_check()
    print("\n" + "="*50 + "\n")
    
    # 测试提取接口
    test_extract_api()
