"""
矛盾纠纷结构化数据提取服务
基于FastAPI的API服务
"""
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import httpx
import json
import logging
import asyncio
from contextlib import asynccontextmanager
from utils import read_system_prompt, extract_json_from_response, validate_extracted_data
from config import (
    LLM_API_URL, LLM_API_KEY, LLM_MODEL,
    MAX_CONCURRENT_REQUESTS, HTTP_POOL_CONFIG, HTTP_TIMEOUT_CONFIG
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局HTTP客户端和并发控制
http_client: Optional[httpx.AsyncClient] = None
# 并发限制信号量，限制同时处理的请求数量
semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global http_client

    # 启动时创建HTTP客户端连接池
    limits = httpx.Limits(**HTTP_POOL_CONFIG)

    timeout = httpx.Timeout(**HTTP_TIMEOUT_CONFIG)

    http_client = httpx.AsyncClient(
        limits=limits,
        timeout=timeout,
        http2=True  # 启用HTTP/2支持
    )

    logger.info(f"HTTP客户端已启动，连接池配置: max_connections={limits.max_connections}, max_keepalive={limits.max_keepalive_connections}")
    logger.info(f"并发限制: {MAX_CONCURRENT_REQUESTS} 个同时请求")

    yield

    # 关闭时清理HTTP客户端
    if http_client:
        await http_client.aclose()
        logger.info("HTTP客户端已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="矛盾纠纷结构化数据提取服务",
    description="提供矛盾纠纷信息的结构化数据提取功能",
    version="1.0.0",
    lifespan=lifespan
)


class PersonInfo(BaseModel):
    """当事人信息"""
    xm: str = Field(description="姓名")
    sfzh: str = Field(description="身份证号码")
    dhhm: str = Field(description="电话号码")


class DisputeInfo(BaseModel):
    """矛盾纠纷信息"""
    lxmc: str = Field(description="矛盾纠纷类型名称")
    lxdm: str = Field(description="矛盾纠纷类型代码")
    dsr: List[PersonInfo] = Field(description="当事人列表")


class ExtractRequest(BaseModel):
    """提取请求模型"""
    query: str = Field(description="查询信息", min_length=1)
    thinking: bool = Field(default=False, description="是否开启深度思考")


class ExtractResponse(BaseModel):
    """提取响应模型"""
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    data: Optional[DisputeInfo] = Field(default=None, description="提取的结构化数据")


@app.get("/")
async def root():
    """根路径"""
    return {"message": "矛盾纠纷结构化数据提取服务"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "concurrent_limit": MAX_CONCURRENT_REQUESTS,
        "available_permits": semaphore._value,
        "active_requests": MAX_CONCURRENT_REQUESTS - semaphore._value,
        "http_client_status": "connected" if http_client else "disconnected"
    }


@app.get("/metrics")
async def get_metrics():
    """获取性能指标"""
    return {
        "concurrent_requests": {
            "max_limit": MAX_CONCURRENT_REQUESTS,
            "available": semaphore._value,
            "active": MAX_CONCURRENT_REQUESTS - semaphore._value,
            "utilization_percent": round((MAX_CONCURRENT_REQUESTS - semaphore._value) / MAX_CONCURRENT_REQUESTS * 100, 2)
        },
        "http_pool": {
            "max_connections": HTTP_POOL_CONFIG["max_connections"],
            "max_keepalive": HTTP_POOL_CONFIG["max_keepalive_connections"],
            "keepalive_expiry": HTTP_POOL_CONFIG["keepalive_expiry"]
        },
        "timeouts": HTTP_TIMEOUT_CONFIG
    }


@app.post("/extract", response_model=ExtractResponse)
async def extract_dispute_info(request: ExtractRequest):
    """
    提取矛盾纠纷结构化数据

    Args:
        request: 包含query和thinking参数的请求

    Returns:
        提取的结构化数据
    """
    # 使用信号量控制并发
    async with semaphore:
        try:
            # 检查HTTP客户端是否可用
            if http_client is None:
                logger.error("HTTP客户端未初始化")
                raise HTTPException(status_code=500, detail="服务暂时不可用")

            # 读取系统提示词
            system_prompt = read_system_prompt()

            # 构建消息列表
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # 如果开启深度思考，添加/think消息
            if not request.thinking:
            #     messages.append({"role": "user", "content": "/think"})
            # else:
                messages.append({"role": "user", "content": "/no_think"})
            # 添加用户查询
            messages.append({"role": "user", "content": request.query})

            logger.info(f"处理请求: {request.query[:50]}...")

            # 构建请求体
            llm_request = {
                "model": LLM_MODEL,
                "messages": messages,
                "stream": False
            }

            # 调用LLM服务 - 使用全局HTTP客户端连接池
            headers = {
                "Authorization": f"Bearer {LLM_API_KEY}",
                "Content-Type": "application/json"
            }

            response = await http_client.post(
                LLM_API_URL,
                json=llm_request,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(f"LLM服务调用失败: {response.status_code}, {response.text}")
                raise HTTPException(
                    status_code=500,
                    detail=f"LLM服务调用失败: {response.status_code}"
                )

            llm_response = response.json()
            logger.debug(f"LLM响应状态: 成功")

            # 提取响应内容
            if "choices" not in llm_response or not llm_response["choices"]:
                raise HTTPException(status_code=500, detail="LLM服务返回格式错误")

            content = llm_response["choices"][0]["message"]["content"]
            logger.info(f"LLM原始响应: {content}")

            # 从响应中提取JSON数据
            extracted_data = extract_json_from_response(content)

            if extracted_data is None:
                logger.warning(f"无法从响应中提取JSON数据: {content}")
                return ExtractResponse(
                    success=False,
                    message="无法从LLM响应中提取有效的JSON数据",
                    data=None
                )

            # 验证数据格式
            if not validate_extracted_data(extracted_data):
                logger.warning(f"提取的数据格式不正确: {extracted_data}")
                return ExtractResponse(
                    success=False,
                    message="提取的数据格式不符合预期",
                    data=None
                )

            # 转换为响应模型
            dispute_info = DisputeInfo(**extracted_data)

            return ExtractResponse(
                success=True,
                message="数据提取成功",
                data=dispute_info
            )

        except FileNotFoundError as e:
            logger.error(f"文件未找到: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

        except httpx.TimeoutException:
            logger.error("LLM服务调用超时")
            raise HTTPException(status_code=500, detail="LLM服务调用超时")

        except httpx.RequestError as e:
            logger.error(f"LLM服务请求错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"LLM服务请求错误: {str(e)}")

        except Exception as e:
            logger.error(f"处理请求时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理请求时发生错误: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
