# YBSS LLM Docker 部署指南

## 概述

本指南提供了将YBSS LLM服务打包成Docker镜像并部署的完整方案，支持开发和生产环境。

## 文件说明

### Docker相关文件

- `Dockerfile` - 开发环境镜像构建文件
- `Dockerfile.prod` - 生产环境优化镜像构建文件（多阶段构建）
- `docker-compose.yml` - Docker Compose编排文件
- `.dockerignore` - Docker构建忽略文件
- `.env.example` - 环境变量配置示例

### 脚本文件

- `build.sh` - 自动化构建脚本
- `deploy.sh` - 自动化部署脚本

## 快速开始

### 1. 准备环境

```bash
# 复制环境变量配置文件
cp .env.example .env

# 根据需要编辑配置
vim .env
```

### 2. 构建镜像

```bash
# 给脚本执行权限
chmod +x build.sh deploy.sh

# 构建镜像（开发版和生产版）
./build.sh

# 或者手动构建
docker build -t ybss-llm:latest -f Dockerfile.prod .
```

### 3. 部署服务

```bash
# 使用部署脚本
./deploy.sh

# 或者使用Docker Compose
docker-compose up -d

# 或者手动运行
docker run -d -p 8000:8000 --name ybss-llm ybss-llm:latest
```

## 详细说明

### 镜像构建

#### 开发版镜像
```bash
docker build -t ybss-llm:dev -f Dockerfile .
```

特点：
- 包含开发工具
- 支持代码热重载
- 适合开发调试

#### 生产版镜像
```bash
docker build -t ybss-llm:latest -f Dockerfile.prod .
```

特点：
- 多阶段构建，镜像更小
- 非root用户运行，更安全
- 优化的依赖安装
- 内置健康检查

### 部署方式

#### 1. 单容器部署

```bash
docker run -d \
  --name ybss-llm \
  -p 8000:8000 \
  -e MAX_CONCURRENT_REQUESTS=100 \
  -e WORKERS=4 \
  --restart unless-stopped \
  ybss-llm:latest
```

#### 2. Docker Compose部署（推荐）

```bash
# 基础部署
docker-compose up -d

# 包含Nginx负载均衡
docker-compose --profile nginx up -d

# 包含监控服务
docker-compose --profile monitoring up -d

# 完整部署
docker-compose --profile nginx --profile monitoring up -d
```

#### 3. 集群部署

```bash
# 启动多个实例
docker-compose up -d --scale ybss-llm=3
```

### 环境变量配置

#### 核心配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `WORKERS` | 4 | Worker进程数量 |
| `MAX_CONCURRENT_REQUESTS` | 100 | 最大并发请求数 |
| `LOG_LEVEL` | info | 日志级别 |

#### 性能配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `HTTP_MAX_CONNECTIONS` | 300 | HTTP最大连接数 |
| `HTTP_MAX_KEEPALIVE` | 150 | HTTP保持连接数 |
| `HTTP_TIMEOUT_READ` | 25.0 | HTTP读取超时 |

#### 资源配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `CPU_LIMIT` | 2.0 | CPU限制 |
| `MEMORY_LIMIT` | 2G | 内存限制 |

### 健康检查

容器内置健康检查：

```bash
# 检查容器健康状态
docker ps

# 手动健康检查
curl http://localhost:8000/health
```

健康检查配置：
- 检查间隔：30秒
- 超时时间：10秒
- 重试次数：3次
- 启动等待：40秒

### 日志管理

#### 查看日志

```bash
# 查看实时日志
docker logs -f ybss-llm

# 查看最近日志
docker logs --tail 100 ybss-llm

# 使用Docker Compose
docker-compose logs -f ybss-llm
```

#### 日志配置

日志自动轮转：
- 单文件最大：10MB
- 保留文件：3个
- 格式：JSON

### 性能监控

#### 内置监控

```bash
# 性能指标
curl http://localhost:8000/metrics

# 健康状态
curl http://localhost:8000/health
```

#### 外部监控（可选）

启用监控服务：

```bash
docker-compose --profile monitoring up -d
```

访问地址：
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin123)

### 故障排查

#### 常见问题

1. **容器启动失败**
```bash
# 查看详细日志
docker logs ybss-llm

# 检查配置
docker inspect ybss-llm
```

2. **健康检查失败**
```bash
# 进入容器检查
docker exec -it ybss-llm /bin/bash

# 手动测试健康检查
curl http://localhost:8000/health
```

3. **性能问题**
```bash
# 查看资源使用
docker stats ybss-llm

# 调整并发配置
docker run -e MAX_CONCURRENT_REQUESTS=50 ...
```

#### 调试模式

```bash
# 开发模式运行
docker run -it --rm \
  -p 8000:8000 \
  -e LOG_LEVEL=debug \
  ybss-llm:dev
```

### 生产部署建议

#### 1. 资源配置

```bash
# 4核8GB服务器推荐配置
export WORKERS=4
export MAX_CONCURRENT_REQUESTS=100
export CPU_LIMIT=3.0
export MEMORY_LIMIT=6G
```

#### 2. 安全配置

- 使用非root用户运行 ✅
- 限制容器权限
- 配置防火墙规则
- 定期更新镜像

#### 3. 高可用部署

```bash
# 多实例部署
docker-compose up -d --scale ybss-llm=3

# 使用外部负载均衡器
# 配置Nginx/HAProxy
```

#### 4. 备份策略

```bash
# 备份配置
cp .env .env.backup

# 导出镜像
docker save ybss-llm:latest > ybss-llm.tar

# 备份数据卷
docker run --rm -v ybss-llm_logs:/data -v $(pwd):/backup alpine tar czf /backup/logs.tar.gz /data
```

### 更新部署

#### 滚动更新

```bash
# 构建新镜像
./build.sh v1.1.0

# 更新服务
docker-compose up -d --no-deps ybss-llm
```

#### 蓝绿部署

```bash
# 启动新版本
docker run -d --name ybss-llm-new -p 8001:8000 ybss-llm:v1.1.0

# 测试新版本
curl http://localhost:8001/health

# 切换流量
docker stop ybss-llm
docker rm ybss-llm
docker run -d --name ybss-llm -p 8000:8000 ybss-llm:v1.1.0
```

## 性能基准

### 测试环境
- 4核8GB服务器
- Docker 20.10+
- 配置：WORKERS=4, MAX_CONCURRENT_REQUESTS=100

### 性能指标
- **吞吐量**: 50-100 RPS
- **并发支持**: 200个并发用户
- **响应时间**: P95 < 5秒
- **成功率**: > 99%

### 压力测试

```bash
# 运行压力测试
python stress_test.py

# 或使用ab工具
ab -n 1000 -c 50 http://localhost:8000/health
```

## 总结

通过Docker化部署，YBSS LLM服务具备了：

✅ **标准化部署** - 一致的运行环境
✅ **弹性扩展** - 支持水平扩展
✅ **资源隔离** - 容器级别的资源控制
✅ **健康监控** - 自动健康检查和恢复
✅ **日志管理** - 统一的日志收集和轮转
✅ **安全运行** - 非root用户和权限控制

现在您可以轻松地在任何支持Docker的环境中部署和运行YBSS LLM服务！
