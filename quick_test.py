"""
快速测试脚本 - 验证优化效果
"""
import requests
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 测试配置
BASE_URL = "http://localhost:8000"
CONCURRENT_REQUESTS = 20  # 快速测试用较小的并发数
TEST_QUERY = "张三和李四因为房屋租赁合同发生争议，张三要求李四支付拖欠的房租5000元，联系电话：13812345678，身份证：110101199001011234"

# 统计数据
stats = {
    "total": 0,
    "success": 0,
    "failed": 0,
    "response_times": [],
    "errors": []
}
stats_lock = threading.Lock()


def make_request(request_id: int):
    """发送单个请求"""
    start_time = time.time()
    try:
        response = requests.post(
            f"{BASE_URL}/extract",
            json={"query": TEST_QUERY, "thinking": False},
            timeout=30
        )
        response_time = time.time() - start_time
        
        with stats_lock:
            stats["total"] += 1
            if response.status_code == 200:
                stats["success"] += 1
                stats["response_times"].append(response_time)
                print(f"Request {request_id}: SUCCESS ({response_time:.2f}s)")
            else:
                stats["failed"] += 1
                stats["errors"].append(f"HTTP {response.status_code}")
                print(f"Request {request_id}: FAILED (HTTP {response.status_code})")
        
        return True, response_time, None
    
    except Exception as e:
        response_time = time.time() - start_time
        with stats_lock:
            stats["total"] += 1
            stats["failed"] += 1
            stats["errors"].append(str(e))
        print(f"Request {request_id}: ERROR ({str(e)})")
        return False, response_time, str(e)


def check_health():
    """检查服务健康状态"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("=== 服务健康状态 ===")
            print(json.dumps(health_data, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"健康检查错误: {e}")
        return False


def check_metrics():
    """检查性能指标"""
    try:
        response = requests.get(f"{BASE_URL}/metrics", timeout=5)
        if response.status_code == 200:
            metrics_data = response.json()
            print("\n=== 性能指标 ===")
            print(json.dumps(metrics_data, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"指标获取失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"指标获取错误: {e}")
        return False


def run_concurrent_test():
    """运行并发测试"""
    print(f"\n=== 并发测试 ({CONCURRENT_REQUESTS} 个并发请求) ===")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=CONCURRENT_REQUESTS) as executor:
        # 提交所有请求
        futures = [
            executor.submit(make_request, i+1) 
            for i in range(CONCURRENT_REQUESTS)
        ]
        
        # 等待所有请求完成
        for future in as_completed(futures):
            future.result()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 计算统计信息
    print(f"\n=== 测试结果 ===")
    print(f"总请求数: {stats['total']}")
    print(f"成功请求: {stats['success']}")
    print(f"失败请求: {stats['failed']}")
    print(f"成功率: {stats['success']/stats['total']*100:.1f}%")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均QPS: {stats['total']/total_time:.1f}")
    
    if stats['response_times']:
        response_times = stats['response_times']
        print(f"响应时间统计:")
        print(f"  最小值: {min(response_times):.2f}秒")
        print(f"  最大值: {max(response_times):.2f}秒")
        print(f"  平均值: {sum(response_times)/len(response_times):.2f}秒")
    
    if stats['errors']:
        print(f"错误类型: {set(stats['errors'])}")


def main():
    """主函数"""
    print("=" * 50)
    print("YBSS LLM 服务快速测试")
    print("=" * 50)
    
    # 1. 检查服务健康状态
    if not check_health():
        print("服务不可用，退出测试")
        return
    
    # 2. 检查性能指标
    check_metrics()
    
    # 3. 运行并发测试
    run_concurrent_test()
    
    # 4. 再次检查指标（查看测试后的状态）
    print("\n=== 测试后的性能指标 ===")
    check_metrics()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
