# 矛盾纠纷结构化数据提取服务

基于FastAPI开发的矛盾纠纷信息结构化提取服务，通过调用LLM服务来提取和结构化矛盾纠纷相关信息。

## 功能特性

- 提供RESTful API接口进行矛盾纠纷信息提取
- 支持深度思考模式
- 自动解析LLM响应并提取结构化JSON数据
- 完整的数据验证和错误处理
- 支持多种矛盾纠纷类型识别

## 项目结构

```
.
├── main.py                    # FastAPI主应用
├── config.py                  # 配置文件
├── utils.py                   # 工具函数
├── run.py                     # 启动脚本
├── test_api.py               # API测试脚本
├── requirements.txt          # 项目依赖
├── README.md                 # 项目说明
└── prompts/
    └── 矛盾纠纷结构化提取.md   # 提示词文件
```

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 方式1: 使用启动脚本
python run.py

# 方式2: 直接使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

服务启动后，可以通过以下地址访问：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 3. 测试API

```bash
python test_api.py
```

## API接口

### POST /extract

提取矛盾纠纷结构化数据

**请求参数:**

```json
{
  "query": "小明和小红因为借款问题发生纠纷",
  "thinking": false
}
```

- `query` (string, 必填): 查询信息，描述矛盾纠纷的具体情况
- `thinking` (boolean, 可选): 是否开启深度思考模式，默认为false

**响应格式:**

```json
{
  "success": true,
  "message": "数据提取成功",
  "data": {
    "lxmc": "民间纠纷",
    "lxdm": "01",
    "dsr": [
      {
        "xm": "小明",
        "sfzh": "",
        "dhhm": ""
      },
      {
        "xm": "小红",
        "sfzh": "",
        "dhhm": ""
      }
    ]
  }
}
```

### GET /health

健康检查接口

**响应:**

```json
{
  "status": "healthy"
}
```

## 矛盾纠纷类型

系统支持以下矛盾纠纷类型：

| 代码 | 类型名称 |
|------|----------|
| 01 | 民间纠纷 |
| 02 | 治安纠纷 |
| 03 | 拖欠农民工工资 |
| 04 | 企业改制 |
| 05 | 邻里纠纷 |
| 06 | 农村征地 |
| 07 | 涉法涉诉 |
| 08 | 城市拆迁 |
| 09 | 库区移民 |
| 10 | 交通运输 |
| 11 | 教师群体 |
| 12 | 出租车行业 |
| 13 | 非法集资 |
| 14 | 对政策不满 |
| 15 | 环境问题 |
| 16 | 矿产纠纷 |
| 17 | 医患纠纷 |
| 18 | 劳资纠纷 |
| 19 | 林地土地草场纠纷 |
| 20 | 供给侧改革 |
| 21 | 经济金融 |
| 22 | 工程建设 |
| 23 | 社会保障 |
| 24 | 突出信访 |
| 25 | 特殊群体 |
| 26 | 涉农 |
| 27 | 婚恋纠纷 |
| 28 | 交通事故 |
| 29 | 劳动保障 |
| 30 | 复转军人安置 |
| 32 | 拖欠社会保障金 |
| 33 | 家庭纠纷 |
| 99 | 其他 |

## 配置说明

在 `config.py` 中可以修改以下配置：

- `LLM_API_URL`: LLM服务的API地址
- `LLM_API_KEY`: LLM服务的API密钥
- `LLM_MODEL`: 使用的模型名称
- `PROMPT_FILE_PATH`: 提示词文件路径

## 注意事项

1. 确保LLM服务正常运行且可访问
2. 提示词文件必须存在且格式正确
3. 当事人是指与具体事件有直接利害关系的人或实体
4. 民警不算当事人

## 错误处理

服务包含完整的错误处理机制：

- 文件不存在错误
- LLM服务调用失败
- 响应解析错误
- 数据格式验证错误
- 网络超时错误

所有错误都会返回相应的HTTP状态码和错误信息。
