# YBSS LLM Docker 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# ===========================================
# 服务器配置
# ===========================================
# 主机端口映射
HOST_PORT=8000

# Worker进程数量（建议：CPU核心数 * 2 + 1）
WORKERS=4

# 日志级别 (debug, info, warning, error)
LOG_LEVEL=info

# ===========================================
# 并发控制配置
# ===========================================
# 最大并发请求数（根据服务器性能调整）
MAX_CONCURRENT_REQUESTS=100

# ===========================================
# HTTP连接池配置
# ===========================================
# 最大保持连接数
HTTP_MAX_KEEPALIVE=150

# 最大连接数
HTTP_MAX_CONNECTIONS=300

# 连接保持时间（秒）
HTTP_KEEPALIVE_EXPIRY=30.0

# ===========================================
# HTTP超时配置
# ===========================================
# 连接超时（秒）
HTTP_TIMEOUT_CONNECT=5.0

# 读取超时（秒）
HTTP_TIMEOUT_READ=25.0

# 写入超时（秒）
HTTP_TIMEOUT_WRITE=5.0

# 连接池超时（秒）
HTTP_TIMEOUT_POOL=2.0

# ===========================================
# 资源限制配置
# ===========================================
# CPU限制
CPU_LIMIT=2.0
CPU_RESERVATION=1.0

# 内存限制
MEMORY_LIMIT=2G
MEMORY_RESERVATION=1G

# ===========================================
# 日志配置
# ===========================================
# 单个日志文件最大大小
LOG_MAX_SIZE=10m

# 保留的日志文件数量
LOG_MAX_FILES=3

# ===========================================
# 负载均衡配置（可选）
# ===========================================
# Nginx端口
NGINX_PORT=80

# ===========================================
# 监控配置（可选）
# ===========================================
# Prometheus端口
PROMETHEUS_PORT=9090

# Grafana端口
GRAFANA_PORT=3000

# Grafana管理员密码
GRAFANA_PASSWORD=admin123

# ===========================================
# 性能调优建议
# ===========================================
# 
# 低配置服务器 (2核4GB):
# WORKERS=2
# MAX_CONCURRENT_REQUESTS=50
# HTTP_MAX_CONNECTIONS=200
# CPU_LIMIT=1.5
# MEMORY_LIMIT=1G
#
# 中等配置服务器 (4核8GB):
# WORKERS=4
# MAX_CONCURRENT_REQUESTS=100
# HTTP_MAX_CONNECTIONS=300
# CPU_LIMIT=2.0
# MEMORY_LIMIT=2G
#
# 高配置服务器 (8核16GB):
# WORKERS=8
# MAX_CONCURRENT_REQUESTS=200
# HTTP_MAX_CONNECTIONS=500
# CPU_LIMIT=4.0
# MEMORY_LIMIT=4G
