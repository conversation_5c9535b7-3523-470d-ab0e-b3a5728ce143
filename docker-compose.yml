version: '3.8'

services:
  ybss-llm:
    build:
      context: .
      dockerfile: Dockerfile.prod
    image: ybss-llm:latest
    container_name: ybss-llm
    ports:
      - "${HOST_PORT:-8000}:8000"
    environment:
      # 服务器配置
      HOST: "0.0.0.0"
      PORT: "8000"
      WORKERS: "${WORKERS:-4}"
      RELOAD: "false"
      LOG_LEVEL: "${LOG_LEVEL:-info}"

      # 并发控制配置
      MAX_CONCURRENT_REQUESTS: "${MAX_CONCURRENT_REQUESTS:-100}"

      # HTTP连接池配置
      HTTP_MAX_KEEPALIVE: "${HTTP_MAX_KEEPALIVE:-150}"
      HTTP_MAX_CONNECTIONS: "${HTTP_MAX_CONNECTIONS:-300}"
      HTTP_KEEPALIVE_EXPIRY: "${HTTP_KEEPALIVE_EXPIRY:-30.0}"

      # HTTP超时配置
      HTTP_TIMEOUT_CONNECT: "${HTTP_TIMEOUT_CONNECT:-5.0}"
      HTTP_TIMEOUT_READ: "${HTTP_TIMEOUT_READ:-25.0}"
      HTTP_TIMEOUT_WRITE: "${HTTP_TIMEOUT_WRITE:-5.0}"
      HTTP_TIMEOUT_POOL: "${HTTP_TIMEOUT_POOL:-2.0}"

    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '${CPU_LIMIT:-2.0}'
          memory: ${MEMORY_LIMIT:-2G}
        reservations:
          cpus: '${CPU_RESERVATION:-1.0}'
          memory: ${MEMORY_RESERVATION:-1G}

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    restart: unless-stopped

    # 挂载卷
    volumes:
      - ./logs:/app/logs

    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "${LOG_MAX_SIZE:-10m}"
        max-file: "${LOG_MAX_FILES:-3}"

  # 负载均衡器（可选）
  nginx:
    image: nginx:alpine
    container_name: ybss-llm-nginx
    ports:
      - "${NGINX_PORT:-80}:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      ybss-llm:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - nginx  # 使用 profile 使 nginx 可选

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: ybss-llm-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: ybss-llm-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  grafana-data:
