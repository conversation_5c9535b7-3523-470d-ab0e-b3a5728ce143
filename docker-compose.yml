version: '3.8'

services:
  ybss-llm:
    build: .
    ports:
      - "8000:8000"
    environment:
      # 服务器配置
      HOST: "0.0.0.0"
      PORT: "8000"
      WORKERS: "4"  # 可根据服务器配置调整
      RELOAD: "false"
      LOG_LEVEL: "info"
      
      # 并发控制配置
      MAX_CONCURRENT_REQUESTS: "100"  # 支持200并发的一半，留出缓冲
      
      # HTTP连接池配置
      HTTP_MAX_KEEPALIVE: "150"
      HTTP_MAX_CONNECTIONS: "300"
      HTTP_KEEPALIVE_EXPIRY: "30.0"
      
      # HTTP超时配置
      HTTP_TIMEOUT_CONNECT: "5.0"
      HTTP_TIMEOUT_READ: "25.0"
      HTTP_TIMEOUT_WRITE: "5.0"
      HTTP_TIMEOUT_POOL: "2.0"
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    restart: unless-stopped
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 可选：添加负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - ybss-llm
    restart: unless-stopped
