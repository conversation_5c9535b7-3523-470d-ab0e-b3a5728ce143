events {
    worker_connections 1024;
}

http {
    upstream ybss_llm_backend {
        # 如果需要多实例负载均衡，可以添加多个server
        server ybss-llm:8000 max_fails=3 fail_timeout=30s;
        # server ybss-llm-2:8000 max_fails=3 fail_timeout=30s;
        # server ybss-llm-3:8000 max_fails=3 fail_timeout=30s;
        
        # 负载均衡策略
        least_conn;  # 最少连接数
    }
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    
    server {
        listen 80;
        server_name localhost;
        
        # 客户端请求体大小限制
        client_max_body_size 10M;
        
        # 超时配置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 健康检查端点
        location /health {
            proxy_pass http://ybss_llm_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 健康检查不需要限流
            access_log off;
        }
        
        # 指标端点
        location /metrics {
            proxy_pass http://ybss_llm_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 限制访问（可选）
            # allow ***********/16;
            # deny all;
        }
        
        # API端点
        location / {
            # 应用限流
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://ybss_llm_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 启用gzip压缩
            gzip on;
            gzip_types application/json text/plain;
            
            # CORS配置（如果需要）
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
            
            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }
    }
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
}
