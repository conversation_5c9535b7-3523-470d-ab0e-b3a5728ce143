"""
工具函数
"""
import re
import json
from typing import Optional
from config import PROMPT_FILE_PATH


def read_system_prompt() -> str:
    """
    从提示词文件中读取系统提示词
    """
    try:
        with open(PROMPT_FILE_PATH, 'r', encoding='utf-8') as f:
            content = f.read()
        return content.strip()
    except FileNotFoundError:
        raise FileNotFoundError(f"提示词文件未找到: {PROMPT_FILE_PATH}")
    except Exception as e:
        raise Exception(f"读取提示词文件失败: {str(e)}")


def extract_json_from_response(response_text: str) -> Optional[dict]:
    """
    从LLM响应中提取JSON数据，忽略<think></think>标签中的内容
    
    Args:
        response_text: LLM的原始响应文本
        
    Returns:
        提取出的JSON数据，如果提取失败返回None
    """
    # 移除<think></think>标签及其内容
    cleaned_text = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL)
    
    # 尝试提取JSON
    # 方法1: 寻找```json代码块
    json_pattern = r'```json\s*(.*?)\s*```'
    match = re.search(json_pattern, cleaned_text, re.DOTALL)
    
    if match:
        json_str = match.group(1).strip()
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            pass
    
    # 方法2: 寻找花括号包围的JSON
    brace_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    matches = re.findall(brace_pattern, cleaned_text, re.DOTALL)
    
    for match in matches:
        try:
            return json.loads(match)
        except json.JSONDecodeError:
            continue
    
    # 方法3: 尝试直接解析整个清理后的文本
    try:
        return json.loads(cleaned_text.strip())
    except json.JSONDecodeError:
        pass
    
    return None


def validate_extracted_data(data: dict) -> bool:
    """
    验证提取的数据是否符合预期格式
    
    Args:
        data: 提取的数据字典
        
    Returns:
        验证是否通过
    """
    if not isinstance(data, dict):
        return False
    
    # 检查必需字段
    required_fields = ['lxmc', 'lxdm', 'dsr']
    for field in required_fields:
        if field not in data:
            return False
    
    # 检查dsr字段是否为列表
    if not isinstance(data['dsr'], list):
        return False
    
    # 检查dsr中每个元素的格式
    for person in data['dsr']:
        if not isinstance(person, dict):
            return False
        # 检查人员信息的必需字段
        person_fields = ['xm', 'sfzh', 'dhhm']
        for field in person_fields:
            if field not in person:
                return False
    
    return True
