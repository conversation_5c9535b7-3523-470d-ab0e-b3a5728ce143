#!/bin/bash

# Docker 快速测试脚本
set -e

echo "=== Docker 环境测试 ==="

# 检查Docker
echo "1. 检查Docker版本..."
docker --version

echo "2. 检查Docker服务..."
docker info > /dev/null && echo "✅ Docker服务正常" || echo "❌ Docker服务异常"

echo "3. 测试基础镜像拉取..."
docker pull python:3.12-slim > /dev/null && echo "✅ 基础镜像拉取成功" || echo "❌ 基础镜像拉取失败"

echo "4. 检查项目文件..."
[ -f "Dockerfile" ] && echo "✅ Dockerfile存在" || echo "❌ Dockerfile不存在"
[ -f "Dockerfile.prod" ] && echo "✅ Dockerfile.prod存在" || echo "❌ Dockerfile.prod不存在"
[ -f "requirements.txt" ] && echo "✅ requirements.txt存在" || echo "❌ requirements.txt不存在"

echo "5. 验证requirements.txt内容..."
grep -q "fastapi" requirements.txt && echo "✅ FastAPI依赖存在" || echo "❌ FastAPI依赖缺失"
grep -q "httpx" requirements.txt && echo "✅ httpx依赖存在" || echo "❌ httpx依赖缺失"

echo ""
echo "=== 准备构建Docker镜像 ==="
echo "所有检查完成，可以开始构建Docker镜像"
echo ""
echo "构建命令："
echo "  开发版: docker build -t ybss-llm:dev -f Dockerfile ."
echo "  生产版: docker build -t ybss-llm:latest -f Dockerfile.prod ."
echo ""
echo "或使用自动化脚本："
echo "  ./build.sh"
