"""
并发压力测试脚本
测试200个并发请求的性能
"""
import asyncio
import aiohttp
import time
import json
from typing import List, Dict, Any
import statistics

# 测试配置
BASE_URL = "http://localhost:8000"
CONCURRENT_REQUESTS = 100
TEST_DURATION = 60  # 测试持续时间（秒）

# 测试数据
TEST_QUERIES = [
    "接警后，民辅警迅速前往现场，经了解在玉龙县黄山街道嘉和丽龙超市附近，报警人是帮忙干不锈钢活路的，现在对方老板欠我们五千元，双方在一起发生纠纷，所以报警求助。民辅警到达现场后，报警人表示，其是被当事人请来安装门的，因当事人觉得安装好的门存在工艺瑕疵，瑕疵部分需要整改，双方就此问题产生口角，在民辅警前往现场途中，其已经与当事人达成协议，将瑕疵部分整改，整改完成后，当事人及时验收，验收完成后立即结清工程款。民辅警对双方表示，既然双方已经达成协议，按照协议履行即可，后续沟通期间双方应冷静沟通，不可有动手等违法犯罪的行为。双方均表示了解。报警人：唐光兴，男，身份证号码：510231197705110419，户籍地址：四川省重庆市原荣昌县，电话号码：18288885761。当事人：姚忠成，男，身份证号码：422425196303251218,户籍地址：湖北省荆州市监利县，电话号码：15287289131。",
    "2025年5月28日16时02分许，在玉龙雪山临时停车场，游客杜明赫（车牌鲁BDC9373）的车被方明（车牌云P17011）的车堵住，在让车过程中杜明赫及同伴胡纯璐与方明发生口角，在比过程中双方无肢体冲突。因事情不大，未造成严重后果。当事双方自愿协商并达成和解，向玉龙雪山派出所申请对此事进行调解处理。民警根据《中国人民共和国治安管理处罚法》第九条之规定，对此事作调解处理。",
    "接警后，值班民警和忠良（142155）出警赶到云南省丽江市古城区西安街道办事处寨后居委会寨后上村10号306室，经了解系报警人杨文玲（53322219970908582x,18687952130）称与老公陈建喝酒后被家暴，民警到达现场后报警人称需前往市医院进行就诊，并称如后续需公安机关帮助，自行前往派出所处理此事。2025年5月28日民警电话联系杨文玲（18687952130）其称两人已经和好，不需要民警帮助，民警已对其进行普法宣传，已告知其不得因此事发生其他过激行为。",
    "2025年5月22日廖国渊反映自己的宠物狗在百盈农贸市场门口被林光勇的三轮车撞了，引发纠纷。",
    "接警后，民警申镇(088261)杨书(059279)出警至现场，了解系报警人和桃(533224199303162525)与老公和彪(533224200112180099，18615768909)因琐事发生口角，后和彪动手打了报警人，因双方手夫妻关手，且报警人不追究其老公责任，因和彪饮酒后任处于亢奋状态，为避免双方在次发生肢体冲突，民警将双方带到派出所继续劝说。",
    "2025年5月15日社区民警电话联系加甲其称目前已与苏金全分手，现加甲已离开丽江回到宁蒗县永宁镇老家务工，暂未有回到丽江的打算，加甲称和苏金全的纠纷已和解，目前双方已无其他纠纷，后续社区民警将持续关注二人动向，二人也称如有需要将会再次联系社区",
    "2025年2月19日肖阿青（533224200306271124，户籍地：云南省丽江市宁蒗彝族自治县蝉战河乡蝉战河村委会新村221号，现住址：云南省丽江市宁蒗彝族自治县大兴街道干河子一组天缘小区，联系电话：15912783821）与金宝华（533224200106080358，户籍地：云南省丽江市宁蒗彝族自治县新营盘乡毛家乡村委会白杨村42号，现住址：云南省丽江市宁蒗彝族自治县大兴街道万格社区七组温馨二院2栋1单元202，联系电话：15912210313）发生婚姻纠纷，金宝华因酒醉打了肖阿青几巴掌导致肖阿青鼻子脆骨部骨折，经巡逻接处警组出警后教育双方不得发生过激行为，移交给社区民辅警进一步调解，后经社区民辅警打电话联系称：肖阿青已起诉在宁蒗县人民法院，两个月后人民法院因双方未领取结婚证进行调解未果，2025年5月19日万格社区电话联系双方家庭成员知名人士到万格社区居委会进行再次调解。肖阿青家人称：先回去与家人商量再处理，暂无成果，需要跟踪及下一步调解处理。",
    "2025年5月28日经万格社区综治中心工作人员、社区民辅警调解下，双方自愿协商达成一致，肖阿青退还85000元人民币和一枚金戒指给金宝华，并签订协议，即日起解除肖阿青与金宝华的婚姻。",
    "据了解，胡涛与其妻子和淑英，2025年5月28日13时许，在中和村41号301室发生争执，争执过程中胡涛鼻子被和淑英弄伤出血，胡涛与和淑英称伤势不重不需要进行伤情鉴定。经调解处理，双方已达成协议，并和解。",
    "值班民警李国勋(142347)、杨志海(142340)接到接处警民警移交警情，经了解系加甲(533224199003122329,13688758628)2025年5月9日18时40分许在西安街道鱼米河撞击台球室内被其老公苏金全(13368883589)殴打，出警民警到场时老公苏金全已离开现场，加甲到所后称要去上班，没时间制作笔录，后加甲一直不到所内配合制作笔录材料，民警多次电话联系加甲，加甲一直不配合到所制作笔录。",
    "2025-05-0916:48:08，名(13688773618)报警称:古城区西安街道花马街东巴谷野山菌，我老公打我，请求帮助。接警后，找到报警人(郭天红:533222197504280040)经了解得知其与丈夫王斌(530113196103260058)因家庭琐事发生争吵，在争吵过程中王斌用手机击打郭天红头部一次，造成郭天红受伤但伤势明显轻微,因两人属于夫妻关系民警对王斌进行批评教育后对其下了家庭暴力告诫书。",
    "2025年5月15日社区民警联合社区工作人员到东巴谷野山菌了解王斌与天红感情纠纷情况，经向当事双方了解该矛盾已化解，得知情况后民已告知王斌(530113196103260058)理性处理感情问题，以后不得再次发生过激行为，合法、合理、合规处理夫妻关系。"
]


class StressTestResult:
    """压力测试结果"""
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.response_times = []
        self.errors = []
        self.start_time = None
        self.end_time = None
    
    def add_result(self, success: bool, response_time: float, error: str = None):
        """添加测试结果"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
            self.response_times.append(response_time)
        else:
            self.failed_requests += 1
            if error:
                self.errors.append(error)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.response_times:
            return {"error": "No successful requests"}
        
        duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        return {
            "test_duration": round(duration, 2),
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": round(self.successful_requests / self.total_requests * 100, 2),
            "requests_per_second": round(self.total_requests / duration, 2) if duration > 0 else 0,
            "response_times": {
                "min": round(min(self.response_times), 3),
                "max": round(max(self.response_times), 3),
                "mean": round(statistics.mean(self.response_times), 3),
                "median": round(statistics.median(self.response_times), 3),
                "p95": round(statistics.quantiles(self.response_times, n=20)[18], 3) if len(self.response_times) >= 20 else "N/A",
                "p99": round(statistics.quantiles(self.response_times, n=100)[98], 3) if len(self.response_times) >= 100 else "N/A"
            },
            "errors": list(set(self.errors))[:10]  # 显示前10个不同的错误
        }


async def make_request(session: aiohttp.ClientSession, query: str) -> tuple[bool, float, str]:
    """发送单个请求"""
    start_time = time.time()
    try:
        async with session.post(
            f"{BASE_URL}/extract",
            json={"query": query, "thinking": False},
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            await response.json()
            response_time = time.time() - start_time
            return response.status == 200, response_time, None
    except Exception as e:
        response_time = time.time() - start_time
        return False, response_time, str(e)


async def worker(session: aiohttp.ClientSession, result: StressTestResult, worker_id: int):
    """工作协程"""
    print(f"Worker {worker_id} started")
    
    while True:
        # 随机选择测试查询
        import random
        query = random.choice(TEST_QUERIES)
        
        success, response_time, error = await make_request(session, query)
        result.add_result(success, response_time, error)
        
        if worker_id == 0 and result.total_requests % 50 == 0:
            print(f"Completed {result.total_requests} requests...")
        
        # 短暂休息避免过度压力
        await asyncio.sleep(0.1)


async def run_stress_test():
    """运行压力测试"""
    print(f"Starting stress test with {CONCURRENT_REQUESTS} concurrent requests")
    print(f"Test duration: {TEST_DURATION} seconds")
    print(f"Target URL: {BASE_URL}")
    
    result = StressTestResult()
    result.start_time = time.time()
    
    # 创建HTTP会话
    connector = aiohttp.TCPConnector(
        limit=CONCURRENT_REQUESTS + 50,  # 连接池大小
        limit_per_host=CONCURRENT_REQUESTS + 50,
        keepalive_timeout=30,
        enable_cleanup_closed=True
    )
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # 创建工作协程
        tasks = [
            asyncio.create_task(worker(session, result, i))
            for i in range(CONCURRENT_REQUESTS)
        ]
        
        # 运行指定时间
        await asyncio.sleep(TEST_DURATION)
        
        # 取消所有任务
        for task in tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
    
    result.end_time = time.time()
    return result


async def check_service_health():
    """检查服务健康状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print("Service health check:")
                    print(json.dumps(health_data, indent=2, ensure_ascii=False))
                    return True
                else:
                    print(f"Health check failed: {response.status}")
                    return False
    except Exception as e:
        print(f"Health check error: {e}")
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("YBSS LLM Service Stress Test")
    print("=" * 60)
    
    # 检查服务健康状态
    if not await check_service_health():
        print("Service is not healthy. Exiting...")
        return
    
    print("\nStarting stress test...")
    
    # 运行压力测试
    result = await run_stress_test()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("STRESS TEST RESULTS")
    print("=" * 60)
    
    stats = result.get_statistics()
    print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    # 性能评估
    print("\n" + "=" * 60)
    print("PERFORMANCE EVALUATION")
    print("=" * 60)
    
    if stats.get("success_rate", 0) >= 95:
        print("✅ SUCCESS RATE: EXCELLENT (≥95%)")
    elif stats.get("success_rate", 0) >= 90:
        print("⚠️  SUCCESS RATE: GOOD (≥90%)")
    else:
        print("❌ SUCCESS RATE: POOR (<90%)")
    
    rps = stats.get("requests_per_second", 0)
    if rps >= 100:
        print("✅ THROUGHPUT: EXCELLENT (≥100 RPS)")
    elif rps >= 50:
        print("⚠️  THROUGHPUT: GOOD (≥50 RPS)")
    else:
        print("❌ THROUGHPUT: POOR (<50 RPS)")
    
    p95 = stats.get("response_times", {}).get("p95", 0)
    if isinstance(p95, (int, float)) and p95 <= 2.0:
        print("✅ LATENCY: EXCELLENT (P95 ≤2s)")
    elif isinstance(p95, (int, float)) and p95 <= 5.0:
        print("⚠️  LATENCY: GOOD (P95 ≤5s)")
    else:
        print("❌ LATENCY: POOR (P95 >5s)")


if __name__ == "__main__":
    asyncio.run(main())
