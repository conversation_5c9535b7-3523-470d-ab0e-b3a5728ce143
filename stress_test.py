"""
并发压力测试脚本
测试200个并发请求的性能
"""
import asyncio
import aiohttp
import time
import json
from typing import List, Dict, Any
import statistics

# 测试配置
BASE_URL = "http://localhost:8000"
CONCURRENT_REQUESTS = 200
TEST_DURATION = 60  # 测试持续时间（秒）

# 测试数据
TEST_QUERIES = [
    "张三和李四因为房屋租赁合同发生争议，张三要求李四支付拖欠的房租5000元，联系电话：13812345678，身份证：110101199001011234",
    "王五和赵六因为交通事故赔偿问题产生纠纷，王五电话：13987654321，身份证：220202199002022345",
    "小明和小红因为借款问题发生纠纷，小明电话：15612345678，身份证：330303199003033456",
    "老张和老李因为邻里噪音问题产生矛盾，老张电话：18712345678，身份证：440404199004044567",
    "企业员工因为工资拖欠问题与公司发生劳资纠纷，员工电话：13512345678，身份证：550505199005055678"
]


class StressTestResult:
    """压力测试结果"""
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.response_times = []
        self.errors = []
        self.start_time = None
        self.end_time = None
    
    def add_result(self, success: bool, response_time: float, error: str = None):
        """添加测试结果"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
            self.response_times.append(response_time)
        else:
            self.failed_requests += 1
            if error:
                self.errors.append(error)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.response_times:
            return {"error": "No successful requests"}
        
        duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        return {
            "test_duration": round(duration, 2),
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": round(self.successful_requests / self.total_requests * 100, 2),
            "requests_per_second": round(self.total_requests / duration, 2) if duration > 0 else 0,
            "response_times": {
                "min": round(min(self.response_times), 3),
                "max": round(max(self.response_times), 3),
                "mean": round(statistics.mean(self.response_times), 3),
                "median": round(statistics.median(self.response_times), 3),
                "p95": round(statistics.quantiles(self.response_times, n=20)[18], 3) if len(self.response_times) >= 20 else "N/A",
                "p99": round(statistics.quantiles(self.response_times, n=100)[98], 3) if len(self.response_times) >= 100 else "N/A"
            },
            "errors": list(set(self.errors))[:10]  # 显示前10个不同的错误
        }


async def make_request(session: aiohttp.ClientSession, query: str) -> tuple[bool, float, str]:
    """发送单个请求"""
    start_time = time.time()
    try:
        async with session.post(
            f"{BASE_URL}/extract",
            json={"query": query, "thinking": False},
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            await response.json()
            response_time = time.time() - start_time
            return response.status == 200, response_time, None
    except Exception as e:
        response_time = time.time() - start_time
        return False, response_time, str(e)


async def worker(session: aiohttp.ClientSession, result: StressTestResult, worker_id: int):
    """工作协程"""
    print(f"Worker {worker_id} started")
    
    while True:
        # 随机选择测试查询
        import random
        query = random.choice(TEST_QUERIES)
        
        success, response_time, error = await make_request(session, query)
        result.add_result(success, response_time, error)
        
        if worker_id == 0 and result.total_requests % 50 == 0:
            print(f"Completed {result.total_requests} requests...")
        
        # 短暂休息避免过度压力
        await asyncio.sleep(0.1)


async def run_stress_test():
    """运行压力测试"""
    print(f"Starting stress test with {CONCURRENT_REQUESTS} concurrent requests")
    print(f"Test duration: {TEST_DURATION} seconds")
    print(f"Target URL: {BASE_URL}")
    
    result = StressTestResult()
    result.start_time = time.time()
    
    # 创建HTTP会话
    connector = aiohttp.TCPConnector(
        limit=CONCURRENT_REQUESTS + 50,  # 连接池大小
        limit_per_host=CONCURRENT_REQUESTS + 50,
        keepalive_timeout=30,
        enable_cleanup_closed=True
    )
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # 创建工作协程
        tasks = [
            asyncio.create_task(worker(session, result, i))
            for i in range(CONCURRENT_REQUESTS)
        ]
        
        # 运行指定时间
        await asyncio.sleep(TEST_DURATION)
        
        # 取消所有任务
        for task in tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
    
    result.end_time = time.time()
    return result


async def check_service_health():
    """检查服务健康状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print("Service health check:")
                    print(json.dumps(health_data, indent=2, ensure_ascii=False))
                    return True
                else:
                    print(f"Health check failed: {response.status}")
                    return False
    except Exception as e:
        print(f"Health check error: {e}")
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("YBSS LLM Service Stress Test")
    print("=" * 60)
    
    # 检查服务健康状态
    if not await check_service_health():
        print("Service is not healthy. Exiting...")
        return
    
    print("\nStarting stress test...")
    
    # 运行压力测试
    result = await run_stress_test()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("STRESS TEST RESULTS")
    print("=" * 60)
    
    stats = result.get_statistics()
    print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    # 性能评估
    print("\n" + "=" * 60)
    print("PERFORMANCE EVALUATION")
    print("=" * 60)
    
    if stats.get("success_rate", 0) >= 95:
        print("✅ SUCCESS RATE: EXCELLENT (≥95%)")
    elif stats.get("success_rate", 0) >= 90:
        print("⚠️  SUCCESS RATE: GOOD (≥90%)")
    else:
        print("❌ SUCCESS RATE: POOR (<90%)")
    
    rps = stats.get("requests_per_second", 0)
    if rps >= 100:
        print("✅ THROUGHPUT: EXCELLENT (≥100 RPS)")
    elif rps >= 50:
        print("⚠️  THROUGHPUT: GOOD (≥50 RPS)")
    else:
        print("❌ THROUGHPUT: POOR (<50 RPS)")
    
    p95 = stats.get("response_times", {}).get("p95", 0)
    if isinstance(p95, (int, float)) and p95 <= 2.0:
        print("✅ LATENCY: EXCELLENT (P95 ≤2s)")
    elif isinstance(p95, (int, float)) and p95 <= 5.0:
        print("⚠️  LATENCY: GOOD (P95 ≤5s)")
    else:
        print("❌ LATENCY: POOR (P95 >5s)")


if __name__ == "__main__":
    asyncio.run(main())
