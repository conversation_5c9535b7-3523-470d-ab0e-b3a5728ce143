"""
启动脚本 - 生产环境优化配置
"""
import uvicorn
import multiprocessing
import os

def get_workers_count():
    """根据CPU核心数计算worker数量"""
    cpu_count = multiprocessing.cpu_count()
    # 推荐的worker数量：CPU核心数 * 2 + 1，但不超过8个
    workers = min(cpu_count * 2 + 1, 8)
    return workers

if __name__ == "__main__":
    # 从环境变量获取配置，提供默认值
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    workers = int(os.getenv("WORKERS", get_workers_count()))
    reload = os.getenv("RELOAD", "false").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "info")

    print(f"启动配置:")
    print(f"  Host: {host}")
    print(f"  Port: {port}")
    print(f"  Workers: {workers}")
    print(f"  Reload: {reload}")
    print(f"  Log Level: {log_level}")
    print(f"  CPU Cores: {multiprocessing.cpu_count()}")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        workers=workers,
        reload=reload,
        log_level=log_level,
        access_log=True,
        # 生产环境优化配置
        loop="uvloop",  # 使用更快的事件循环
        http="httptools",  # 使用更快的HTTP解析器
        # 连接配置
        backlog=2048,  # 增加连接队列大小
        # 超时配置
        timeout_keep_alive=5,  # Keep-alive超时
        timeout_graceful_shutdown=30,  # 优雅关闭超时
    )
