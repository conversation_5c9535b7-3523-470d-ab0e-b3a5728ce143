#!/bin/bash

# YBSS LLM Docker 构建脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ybss-llm"
VERSION=${1:-"latest"}
REGISTRY=${REGISTRY:-""}  # 可以设置为你的镜像仓库地址

echo -e "${BLUE}=== YBSS LLM Docker 构建脚本 ===${NC}"
echo -e "${BLUE}镜像名称: ${IMAGE_NAME}${NC}"
echo -e "${BLUE}版本标签: ${VERSION}${NC}"

# 函数：打印带颜色的消息
print_step() {
    echo -e "${GREEN}[步骤] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[警告] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

# 检查Docker是否安装
check_docker() {
    print_step "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    print_info "Docker环境检查通过"
}

# 清理旧镜像
cleanup_old_images() {
    print_step "清理旧的镜像..."
    
    # 删除悬空镜像
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) || true
        print_info "已清理悬空镜像"
    else
        print_info "没有悬空镜像需要清理"
    fi
}

# 构建开发版镜像
build_dev() {
    print_step "构建开发版镜像..."
    docker build -t ${IMAGE_NAME}:${VERSION}-dev -f Dockerfile .
    print_info "开发版镜像构建完成: ${IMAGE_NAME}:${VERSION}-dev"
}

# 构建生产版镜像
build_prod() {
    print_step "构建生产版镜像..."
    docker build -t ${IMAGE_NAME}:${VERSION} -f Dockerfile.prod .
    print_info "生产版镜像构建完成: ${IMAGE_NAME}:${VERSION}"
}

# 测试镜像
test_image() {
    local image_tag=$1
    print_step "测试镜像: ${image_tag}"
    
    # 启动容器进行测试
    print_info "启动测试容器..."
    container_id=$(docker run -d -p 8001:8000 --name ybss-llm-test ${image_tag})
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 10
    
    # 健康检查
    print_info "执行健康检查..."
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        print_info "✅ 健康检查通过"
        test_result=0
    else
        print_error "❌ 健康检查失败"
        test_result=1
    fi
    
    # 清理测试容器
    print_info "清理测试容器..."
    docker stop ${container_id} > /dev/null
    docker rm ${container_id} > /dev/null
    
    return $test_result
}

# 显示镜像信息
show_image_info() {
    print_step "镜像信息:"
    echo ""
    docker images | grep ${IMAGE_NAME} | head -5
    echo ""
    
    # 显示镜像大小
    if docker images ${IMAGE_NAME}:${VERSION} --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep -q ${VERSION}; then
        print_info "生产版镜像大小: $(docker images ${IMAGE_NAME}:${VERSION} --format '{{.Size}}')"
    fi
    
    if docker images ${IMAGE_NAME}:${VERSION}-dev --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}" | grep -q ${VERSION}-dev; then
        print_info "开发版镜像大小: $(docker images ${IMAGE_NAME}:${VERSION}-dev --format '{{.Size}}')"
    fi
}

# 推送镜像到仓库
push_image() {
    if [ -n "$REGISTRY" ]; then
        print_step "推送镜像到仓库..."
        
        # 标记镜像
        docker tag ${IMAGE_NAME}:${VERSION} ${REGISTRY}/${IMAGE_NAME}:${VERSION}
        
        # 推送镜像
        docker push ${REGISTRY}/${IMAGE_NAME}:${VERSION}
        print_info "镜像已推送到: ${REGISTRY}/${IMAGE_NAME}:${VERSION}"
    else
        print_warning "未设置REGISTRY环境变量，跳过推送步骤"
        print_info "如需推送，请设置REGISTRY环境变量，例如: export REGISTRY=your-registry.com"
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    print_step "使用说明:"
    echo ""
    echo "1. 运行开发版容器:"
    echo "   docker run -d -p 8000:8000 --name ybss-llm-dev ${IMAGE_NAME}:${VERSION}-dev"
    echo ""
    echo "2. 运行生产版容器:"
    echo "   docker run -d -p 8000:8000 --name ybss-llm ${IMAGE_NAME}:${VERSION}"
    echo ""
    echo "3. 使用环境变量配置:"
    echo "   docker run -d -p 8000:8000 \\"
    echo "     -e MAX_CONCURRENT_REQUESTS=100 \\"
    echo "     -e WORKERS=8 \\"
    echo "     --name ybss-llm ${IMAGE_NAME}:${VERSION}"
    echo ""
    echo "4. 使用Docker Compose:"
    echo "   docker-compose up -d"
    echo ""
    echo "5. 健康检查:"
    echo "   curl http://localhost:8000/health"
    echo ""
}

# 主函数
main() {
    check_docker
    cleanup_old_images
    
    # 构建镜像
    build_dev
    build_prod
    
    # 测试生产版镜像
    if test_image "${IMAGE_NAME}:${VERSION}"; then
        print_info "✅ 镜像测试通过"
    else
        print_error "❌ 镜像测试失败"
        exit 1
    fi
    
    # 显示镜像信息
    show_image_info
    
    # 推送镜像（如果配置了仓库）
    push_image
    
    # 显示使用说明
    show_usage
    
    print_step "构建完成! 🎉"
}

# 执行主函数
main
