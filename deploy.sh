#!/bin/bash

# YBSS LLM Docker 部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ybss-llm"
CONTAINER_NAME="ybss-llm"
VERSION=${1:-"latest"}
PORT=${2:-"8000"}

echo -e "${BLUE}=== YBSS LLM Docker 部署脚本 ===${NC}"

# 函数定义
print_step() {
    echo -e "${GREEN}[步骤] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[警告] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

# 检查镜像是否存在
check_image() {
    print_step "检查镜像..."
    if ! docker images ${IMAGE_NAME}:${VERSION} | grep -q ${VERSION}; then
        print_error "镜像 ${IMAGE_NAME}:${VERSION} 不存在"
        print_info "请先运行构建脚本: ./build.sh ${VERSION}"
        exit 1
    fi
    print_info "镜像检查通过"
}

# 停止并删除旧容器
cleanup_old_container() {
    print_step "清理旧容器..."
    
    if docker ps -a | grep -q ${CONTAINER_NAME}; then
        print_info "停止旧容器..."
        docker stop ${CONTAINER_NAME} > /dev/null 2>&1 || true
        
        print_info "删除旧容器..."
        docker rm ${CONTAINER_NAME} > /dev/null 2>&1 || true
    fi
    
    print_info "容器清理完成"
}

# 启动新容器
start_container() {
    print_step "启动新容器..."
    
    docker run -d \
        --name ${CONTAINER_NAME} \
        -p ${PORT}:8000 \
        -e MAX_CONCURRENT_REQUESTS=50 \
        -e WORKERS=4 \
        -e LOG_LEVEL=info \
        --restart unless-stopped \
        --health-cmd="curl -f http://localhost:8000/health || exit 1" \
        --health-interval=30s \
        --health-timeout=10s \
        --health-retries=3 \
        --health-start-period=40s \
        ${IMAGE_NAME}:${VERSION}
    
    print_info "容器已启动: ${CONTAINER_NAME}"
}

# 等待服务就绪
wait_for_service() {
    print_step "等待服务就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:${PORT}/health > /dev/null 2>&1; then
            print_info "✅ 服务已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "❌ 服务启动超时"
    return 1
}

# 显示服务状态
show_status() {
    print_step "服务状态:"
    echo ""
    
    # 容器状态
    docker ps | grep ${CONTAINER_NAME} || print_warning "容器未运行"
    echo ""
    
    # 健康检查
    print_info "健康检查:"
    curl -s http://localhost:${PORT}/health | python3 -m json.tool 2>/dev/null || print_warning "健康检查失败"
    echo ""
    
    # 性能指标
    print_info "性能指标:"
    curl -s http://localhost:${PORT}/metrics | python3 -m json.tool 2>/dev/null || print_warning "指标获取失败"
    echo ""
}

# 显示日志
show_logs() {
    print_step "最近日志:"
    echo ""
    docker logs --tail 20 ${CONTAINER_NAME}
    echo ""
}

# 显示管理命令
show_management_commands() {
    print_step "管理命令:"
    echo ""
    echo "查看日志:"
    echo "  docker logs -f ${CONTAINER_NAME}"
    echo ""
    echo "进入容器:"
    echo "  docker exec -it ${CONTAINER_NAME} /bin/bash"
    echo ""
    echo "重启容器:"
    echo "  docker restart ${CONTAINER_NAME}"
    echo ""
    echo "停止容器:"
    echo "  docker stop ${CONTAINER_NAME}"
    echo ""
    echo "删除容器:"
    echo "  docker rm -f ${CONTAINER_NAME}"
    echo ""
    echo "查看资源使用:"
    echo "  docker stats ${CONTAINER_NAME}"
    echo ""
}

# 性能测试
run_performance_test() {
    print_step "运行性能测试..."
    
    if [ -f "quick_test.py" ]; then
        print_info "运行快速测试..."
        python3 quick_test.py
    else
        print_warning "quick_test.py 不存在，跳过性能测试"
        print_info "手动测试命令:"
        echo "curl -X POST http://localhost:${PORT}/extract \\"
        echo "  -H 'Content-Type: application/json' \\"
        echo "  -d '{\"query\": \"测试查询\", \"thinking\": false}'"
    fi
}

# 主函数
main() {
    print_info "镜像: ${IMAGE_NAME}:${VERSION}"
    print_info "端口: ${PORT}"
    print_info "容器名: ${CONTAINER_NAME}"
    echo ""
    
    check_image
    cleanup_old_container
    start_container
    
    if wait_for_service; then
        show_status
        show_logs
        show_management_commands
        
        # 询问是否运行性能测试
        echo ""
        read -p "是否运行性能测试? (y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            run_performance_test
        fi
        
        print_step "部署完成! 🎉"
        print_info "服务地址: http://localhost:${PORT}"
        print_info "健康检查: http://localhost:${PORT}/health"
        print_info "性能指标: http://localhost:${PORT}/metrics"
    else
        print_error "部署失败"
        print_info "查看容器日志:"
        docker logs ${CONTAINER_NAME}
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [VERSION] [PORT]"
    echo ""
    echo "参数:"
    echo "  VERSION  镜像版本 (默认: latest)"
    echo "  PORT     服务端口 (默认: 8000)"
    echo ""
    echo "示例:"
    echo "  $0                    # 使用默认参数"
    echo "  $0 v1.0.0             # 指定版本"
    echo "  $0 latest 8080        # 指定版本和端口"
    echo ""
}

# 检查参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 执行主函数
main
