#!/bin/bash

# 简单的Dockerfile语法检查脚本
echo "=== Dockerfile 语法检查 ==="

# 检查FROM AS大小写
echo "1. 检查FROM AS大小写一致性..."

for dockerfile in Dockerfile Dockerfile.prod Dockerfile.china Dockerfile.china.prod; do
    if [ -f "$dockerfile" ]; then
        echo "检查 $dockerfile:"
        
        # 检查是否有小写的as
        if grep -n "FROM.*as " "$dockerfile"; then
            echo "  ❌ 发现小写 'as'，应该使用大写 'AS'"
        else
            echo "  ✅ FROM AS 大小写正确"
        fi
        
        # 检查是否有大写的AS
        if grep -n "FROM.*AS " "$dockerfile"; then
            echo "  ✅ 使用了正确的大写 'AS'"
        fi
        
        echo ""
    fi
done

echo "2. 检查其他常见问题..."

for dockerfile in Dockerfile Dockerfile.prod Dockerfile.china Dockerfile.china.prod; do
    if [ -f "$dockerfile" ]; then
        echo "检查 $dockerfile:"
        
        # 检查是否使用了非root用户
        if grep -q "USER " "$dockerfile"; then
            echo "  ✅ 使用了非root用户"
        else
            echo "  ⚠️  建议使用非root用户"
        fi
        
        # 检查是否有健康检查
        if grep -q "HEALTHCHECK" "$dockerfile"; then
            echo "  ✅ 包含健康检查"
        else
            echo "  ⚠️  建议添加健康检查"
        fi
        
        # 检查是否清理了apt缓存
        if grep -q "apt-get install" "$dockerfile" && grep -q "rm -rf /var/lib/apt/lists" "$dockerfile"; then
            echo "  ✅ 正确清理了apt缓存"
        elif grep -q "apt-get install" "$dockerfile"; then
            echo "  ⚠️  建议清理apt缓存"
        fi
        
        echo ""
    fi
done

echo "3. 验证构建语法..."

# 简单的语法验证
for dockerfile in Dockerfile.china.prod; do
    if [ -f "$dockerfile" ]; then
        echo "验证 $dockerfile 构建语法..."
        
        # 检查基本语法
        if grep -q "^FROM " "$dockerfile" && grep -q "^WORKDIR " "$dockerfile"; then
            echo "  ✅ 基本语法结构正确"
        else
            echo "  ❌ 基本语法可能有问题"
        fi
    fi
done

echo ""
echo "=== 修复建议 ==="
echo ""
echo "如果发现小写 'as' 问题，可以使用以下命令修复："
echo "sed -i 's/FROM\\(.*\\)as /FROM\\1AS /g' Dockerfile*"
echo ""
echo "检查完成！"
