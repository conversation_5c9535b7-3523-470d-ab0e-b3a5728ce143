"""
配置文件
"""
import os

# LLM服务配置
LLM_API_URL = "http://172.168.0.3:19091/v1/chat/completions"
LLM_API_KEY = "sk-NzzAZPkRg5EtqG4dvbnohvz9UMNx4TtXaWk8SKp6dVlo0XsQ"
LLM_MODEL = "SK-General"

# 提示词文件路径
PROMPT_FILE_PATH = "prompts/矛盾纠纷结构化提取.md"

# 并发控制配置
MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", "50"))

# HTTP客户端连接池配置
HTTP_POOL_CONFIG = {
    "max_keepalive_connections": int(os.getenv("HTTP_MAX_KEEPALIVE", "100")),
    "max_connections": int(os.getenv("HTTP_MAX_CONNECTIONS", "200")),
    "keepalive_expiry": float(os.getenv("HTTP_KEEPALIVE_EXPIRY", "30.0"))
}

# HTTP超时配置
HTTP_TIMEOUT_CONFIG = {
    "connect": float(os.getenv("HTTP_TIMEOUT_CONNECT", "5.0")),
    "read": float(os.getenv("HTTP_TIMEOUT_READ", "25.0")),
    "write": float(os.getenv("HTTP_TIMEOUT_WRITE", "5.0")),
    "pool": float(os.getenv("HTTP_TIMEOUT_POOL", "2.0"))
}
