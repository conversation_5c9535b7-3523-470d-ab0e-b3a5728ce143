#!/bin/bash

# Docker Dockerfile 语法检查和最佳实践验证脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Docker Dockerfile 语法检查 ===${NC}"

# 函数定义
print_step() {
    echo -e "${GREEN}[检查] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[警告] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

print_success() {
    echo -e "${GREEN}[成功] $1${NC}"
}

# 检查Docker是否安装
check_docker() {
    print_step "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    print_success "Docker环境正常"
}

# 检查Dockerfile语法
check_dockerfile_syntax() {
    local dockerfile=$1
    local name=$2
    
    print_step "检查 ${name} 语法..."
    
    if [ ! -f "$dockerfile" ]; then
        print_warning "${dockerfile} 不存在，跳过检查"
        return
    fi
    
    # 使用docker build --dry-run检查语法（如果支持）
    if docker build --help | grep -q "\-\-dry-run"; then
        if docker build --dry-run -f "$dockerfile" . > /dev/null 2>&1; then
            print_success "${name} 语法正确"
        else
            print_error "${name} 语法错误"
            docker build --dry-run -f "$dockerfile" . 2>&1 | head -10
        fi
    else
        # 使用基本语法检查
        if docker build -f "$dockerfile" --target builder . > /dev/null 2>&1 || \
           docker build -f "$dockerfile" . > /dev/null 2>&1; then
            print_success "${name} 语法正确"
        else
            print_error "${name} 可能存在语法问题"
        fi
    fi
}

# 检查Dockerfile最佳实践
check_dockerfile_best_practices() {
    local dockerfile=$1
    local name=$2
    
    print_step "检查 ${name} 最佳实践..."
    
    if [ ! -f "$dockerfile" ]; then
        print_warning "${dockerfile} 不存在，跳过检查"
        return
    fi
    
    local issues=0
    
    # 检查FROM AS大小写一致性
    if grep -q "FROM.*as " "$dockerfile"; then
        print_warning "${name}: 建议使用大写 'AS' 而不是小写 'as'"
        echo "  修复: sed -i 's/FROM\\(.*\\)as /FROM\\1AS /g' $dockerfile"
        issues=$((issues + 1))
    fi
    
    # 检查是否使用了具体版本标签
    if grep -q "FROM.*:latest" "$dockerfile"; then
        print_warning "${name}: 建议使用具体版本标签而不是 'latest'"
        issues=$((issues + 1))
    fi
    
    # 检查是否有多个RUN命令可以合并
    local run_count=$(grep -c "^RUN " "$dockerfile" || true)
    if [ "$run_count" -gt 3 ]; then
        print_warning "${name}: 发现 ${run_count} 个RUN指令，考虑合并以减少镜像层数"
        issues=$((issues + 1))
    fi
    
    # 检查是否清理了包管理器缓存
    if grep -q "apt-get install" "$dockerfile" && ! grep -q "rm -rf /var/lib/apt/lists" "$dockerfile"; then
        print_warning "${name}: 建议在apt-get install后清理缓存"
        issues=$((issues + 1))
    fi
    
    # 检查是否使用了非root用户
    if ! grep -q "USER " "$dockerfile"; then
        print_warning "${name}: 建议使用非root用户运行容器"
        issues=$((issues + 1))
    fi
    
    # 检查是否有健康检查
    if ! grep -q "HEALTHCHECK" "$dockerfile"; then
        print_warning "${name}: 建议添加健康检查"
        issues=$((issues + 1))
    fi
    
    if [ $issues -eq 0 ]; then
        print_success "${name} 遵循了Docker最佳实践"
    else
        print_info "${name} 发现 ${issues} 个可改进的地方"
    fi
}

# 使用hadolint检查（如果可用）
check_with_hadolint() {
    local dockerfile=$1
    local name=$2
    
    if command -v hadolint &> /dev/null; then
        print_step "使用hadolint检查 ${name}..."
        if hadolint "$dockerfile"; then
            print_success "${name} hadolint检查通过"
        else
            print_warning "${name} hadolint发现了一些问题"
        fi
    else
        print_info "hadolint未安装，跳过高级检查"
        print_info "安装hadolint: https://github.com/hadolint/hadolint"
    fi
}

# 检查.dockerignore文件
check_dockerignore() {
    print_step "检查.dockerignore文件..."
    
    if [ -f ".dockerignore" ]; then
        print_success ".dockerignore文件存在"
        
        # 检查常见的忽略项
        local missing_items=()
        
        if ! grep -q "__pycache__" .dockerignore; then
            missing_items+=("__pycache__")
        fi
        
        if ! grep -q "\.git" .dockerignore; then
            missing_items+=(".git")
        fi
        
        if ! grep -q "\.pytest_cache" .dockerignore; then
            missing_items+=(".pytest_cache")
        fi
        
        if [ ${#missing_items[@]} -gt 0 ]; then
            print_warning "建议在.dockerignore中添加: ${missing_items[*]}"
        else
            print_success ".dockerignore配置良好"
        fi
    else
        print_warning ".dockerignore文件不存在，建议创建以优化构建"
    fi
}

# 检查构建上下文大小
check_build_context() {
    print_step "检查构建上下文大小..."
    
    # 计算当前目录大小（排除.dockerignore中的文件）
    local context_size=$(du -sh . 2>/dev/null | cut -f1)
    print_info "构建上下文大小: ${context_size}"
    
    # 检查是否有大文件
    print_info "检查大文件..."
    find . -type f -size +10M 2>/dev/null | head -5 | while read -r file; do
        if [ -n "$file" ]; then
            local size=$(du -sh "$file" | cut -f1)
            print_warning "发现大文件: $file (${size})"
        fi
    done
}

# 生成修复建议
generate_fix_suggestions() {
    print_step "生成修复建议..."
    
    echo ""
    echo -e "${BLUE}=== 修复建议 ===${NC}"
    echo ""
    
    echo "1. 修复FROM AS大小写问题:"
    echo "   sed -i 's/FROM\\(.*\\)as /FROM\\1AS /g' Dockerfile*"
    echo ""
    
    echo "2. 安装hadolint进行高级检查:"
    echo "   # macOS"
    echo "   brew install hadolint"
    echo "   # Linux"
    echo "   wget -O hadolint https://github.com/hadolint/hadolint/releases/latest/download/hadolint-Linux-x86_64"
    echo "   chmod +x hadolint && sudo mv hadolint /usr/local/bin/"
    echo ""
    
    echo "3. 使用Docker BuildKit获得更好的构建体验:"
    echo "   export DOCKER_BUILDKIT=1"
    echo "   docker build ..."
    echo ""
    
    echo "4. 启用Docker实验性功能:"
    echo "   export DOCKER_CLI_EXPERIMENTAL=enabled"
    echo ""
}

# 主函数
main() {
    check_docker
    echo ""
    
    # 检查所有Dockerfile
    check_dockerfile_syntax "Dockerfile" "开发版Dockerfile"
    check_dockerfile_best_practices "Dockerfile" "开发版Dockerfile"
    echo ""
    
    check_dockerfile_syntax "Dockerfile.prod" "生产版Dockerfile"
    check_dockerfile_best_practices "Dockerfile.prod" "生产版Dockerfile"
    echo ""
    
    check_dockerfile_syntax "Dockerfile.china" "中国优化版Dockerfile"
    check_dockerfile_best_practices "Dockerfile.china" "中国优化版Dockerfile"
    echo ""
    
    check_dockerfile_syntax "Dockerfile.china.prod" "中国优化生产版Dockerfile"
    check_dockerfile_best_practices "Dockerfile.china.prod" "中国优化生产版Dockerfile"
    echo ""
    
    # 使用hadolint检查
    check_with_hadolint "Dockerfile" "开发版Dockerfile"
    check_with_hadolint "Dockerfile.prod" "生产版Dockerfile"
    check_with_hadolint "Dockerfile.china" "中国优化版Dockerfile"
    check_with_hadolint "Dockerfile.china.prod" "中国优化生产版Dockerfile"
    echo ""
    
    # 其他检查
    check_dockerignore
    echo ""
    
    check_build_context
    echo ""
    
    generate_fix_suggestions
    
    print_step "检查完成! 🎉"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "功能:"
    echo "  - 检查Dockerfile语法"
    echo "  - 验证Docker最佳实践"
    echo "  - 使用hadolint进行高级检查"
    echo "  - 检查.dockerignore配置"
    echo "  - 分析构建上下文"
    echo ""
}

# 检查参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 执行主函数
main
